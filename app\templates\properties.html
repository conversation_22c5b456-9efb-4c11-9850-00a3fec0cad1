<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Properties - DreamBig Real Estate</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/auth.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .properties-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .properties-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }

        .search-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 40px;
        }

        .search-bar {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 2;
            min-width: 300px;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 50px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .search-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            transition: transform 0.2s ease;
            white-space: nowrap;
        }

        .search-button:hover {
            transform: translateY(-2px);
        }

        .filters-toggle {
            background: #f8f9fa;
            border: 2px solid #e1e5e9;
            padding: 15px 25px;
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            color: #667eea;
            transition: all 0.3s ease;
        }

        .filters-toggle:hover {
            background: #667eea;
            color: white;
        }

        .filters-section {
            display: none;
            margin-top: 25px;
            padding-top: 25px;
            border-top: 1px solid #e1e5e9;
        }

        .filters-section.active {
            display: block;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .filter-group select,
        .filter-group input {
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .properties-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .property-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .property-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .property-image {
            height: 250px;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .property-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .property-content {
            padding: 25px;
        }

        .property-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .property-location {
            color: #666;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .property-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .property-specs {
            display: flex;
            gap: 15px;
            font-size: 0.9rem;
            color: #666;
        }

        .property-price {
            font-size: 1.4rem;
            font-weight: 700;
            color: #667eea;
        }

        .property-actions {
            display: flex;
            gap: 10px;
        }

        .action-button {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .btn-favorite {
            background: #ff4757;
            color: white;
        }

        .btn-favorite.active {
            background: #ff3742;
        }

        .action-button:hover {
            transform: translateY(-2px);
        }

        .loading-message {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-message {
            text-align: center;
            padding: 60px;
            color: #666;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .pagination button {
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover,
        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .add-property-fab {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .add-property-fab:hover {
            transform: scale(1.1);
        }

        .suggestions-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #e1e5e9;
            border-top: none;
            border-radius: 0 0 10px 10px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .suggestion-item {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
        }

        .suggestion-item:hover {
            background: #f8f9fa;
        }

        @media (max-width: 768px) {
            .properties-container {
                padding: 15px;
            }

            .search-bar {
                flex-direction: column;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .properties-grid {
                grid-template-columns: 1fr;
            }

            .property-details {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'components/navbar.html' %}

    <div class="properties-container">
        <!-- Properties Header -->
        <div class="properties-header">
            <h1>Find Your Perfect Property</h1>
            <p>Discover thousands of verified properties with AI-powered recommendations</p>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <div class="search-bar">
                <div style="position: relative; flex: 2;">
                    <input type="text" class="search-input" id="searchInput"
                           placeholder="Search by location, property type, or keywords...">
                    <div class="suggestions-dropdown" id="suggestionsDropdown"></div>
                </div>
                <button class="search-button" onclick="searchProperties()">
                    <i class="fas fa-search"></i> Search
                </button>
                <button class="filters-toggle" onclick="toggleFilters()">
                    <i class="fas fa-filter"></i> Filters
                </button>
            </div>

            <div class="filters-section" id="filtersSection">
                <div class="filters-grid">
                <div class="filter-group">
                    <label>Property Type</label>
                    <select id="propertyType">
                        <option value="">All Types</option>
                        <option value="apartment">Apartment</option>
                        <option value="house">House</option>
                        <option value="villa">Villa</option>
                        <option value="commercial">Commercial</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>BHK</label>
                    <select id="bhkFilter">
                        <option value="">Any BHK</option>
                        <option value="1">1 BHK</option>
                        <option value="2">2 BHK</option>
                        <option value="3">3 BHK</option>
                        <option value="4">4+ BHK</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>Min Price (₹)</label>
                    <input type="number" id="minPrice" placeholder="Min Price">
                </div>

                <div class="filter-group">
                    <label>Max Price (₹)</label>
                    <input type="number" id="maxPrice" placeholder="Max Price">
                </div>

                <div class="filter-group">
                    <label>Furnishing</label>
                    <select id="furnishing">
                        <option value="">Any</option>
                        <option value="furnished">Furnished</option>
                        <option value="semi_furnished">Semi-Furnished</option>
                        <option value="unfurnished">Unfurnished</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>Location</label>
                    <input type="text" id="locationFilter" placeholder="City or Area">
                </div>
                </div>
            </div>
        </div>

        <!-- Properties Grid -->
        <div class="properties-grid" id="propertiesGrid">
            <div class="loading-message">Loading properties...</div>
        </div>

        <!-- Pagination -->
        <div class="pagination" id="pagination" style="display: none;"></div>

        <!-- Add Property FAB -->
        <button class="add-property-fab" onclick="addProperty()" title="Add New Property">
            <i class="fas fa-plus"></i>
        </button>

        <!-- Success/Error Messages -->
        <div class="message-container" id="messageContainer"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='/js/firebase-config.js') }}"></script>
    <script src="{{ url_for('static', path='/js/auth.js') }}"></script>
    <script src="{{ url_for('static', path='/js/properties.js') }}"></script>
    
    <script>
        // Toggle filters visibility
        function toggleFilters() {
            const filtersSection = document.getElementById('filtersSection');
            const toggleButton = document.querySelector('.filters-toggle');

            filtersSection.classList.toggle('active');

            if (filtersSection.classList.contains('active')) {
                toggleButton.innerHTML = '<i class="fas fa-times"></i> Hide Filters';
            } else {
                toggleButton.innerHTML = '<i class="fas fa-filter"></i> Filters';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            initializeProperties();
        });
    </script>
</body>
</html>
