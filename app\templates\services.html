<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services - DreamBig Real Estate</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/auth.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .services-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .services-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }

        .services-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .category-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .category-card:hover {
            transform: translateY(-5px);
        }

        .category-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 20px;
        }

        .category-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .category-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .category-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .service-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .service-card:hover {
            transform: translateY(-5px);
        }

        .service-image {
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            position: relative;
        }

        .service-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
        }

        .service-content {
            padding: 20px;
        }

        .service-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .service-provider {
            color: #666;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .service-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: #666;
        }

        .service-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 15px;
        }

        .service-actions {
            display: flex;
            gap: 10px;
        }

        .action-button {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .search-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .search-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .search-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
        }

        .search-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .filter-group select {
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .loading-message {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-message {
            text-align: center;
            padding: 60px;
            color: #666;
        }

        @media (max-width: 768px) {
            .services-container {
                padding: 15px;
            }

            .search-bar {
                flex-direction: column;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }

            .services-categories {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'components/navbar.html' %}

    <div class="services-container">
        <!-- Services Header -->
        <div class="services-header">
            <h1>Professional Services</h1>
            <p>Connect with verified service providers for all your real estate needs</p>
        </div>

        <!-- Service Categories -->
        <div class="services-categories">
            <div class="category-card" onclick="filterByCategory('legal')">
                <div class="category-icon">
                    <i class="fas fa-gavel"></i>
                </div>
                <div class="category-title">Legal Services</div>
                <div class="category-description">
                    Property documentation, legal verification, and consultation services
                </div>
                <button class="category-button">Browse Legal Services</button>
            </div>

            <div class="category-card" onclick="filterByCategory('financial')">
                <div class="category-icon">
                    <i class="fas fa-calculator"></i>
                </div>
                <div class="category-title">Financial Services</div>
                <div class="category-description">
                    Home loans, investment advice, and financial planning services
                </div>
                <button class="category-button">Browse Financial Services</button>
            </div>

            <div class="category-card" onclick="filterByCategory('maintenance')">
                <div class="category-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div class="category-title">Maintenance</div>
                <div class="category-description">
                    Property maintenance, repairs, and renovation services
                </div>
                <button class="category-button">Browse Maintenance</button>
            </div>

            <div class="category-card" onclick="filterByCategory('inspection')">
                <div class="category-icon">
                    <i class="fas fa-search"></i>
                </div>
                <div class="category-title">Inspection</div>
                <div class="category-description">
                    Property inspection, valuation, and assessment services
                </div>
                <button class="category-button">Browse Inspection</button>
            </div>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <div class="search-bar">
                <input type="text" class="search-input" id="searchInput" 
                       placeholder="Search for services, providers, or locations...">
                <button class="search-button" onclick="searchServices()">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>

            <div class="filters-grid">
                <div class="filter-group">
                    <label>Service Category</label>
                    <select id="categoryFilter">
                        <option value="">All Categories</option>
                        <option value="legal">Legal Services</option>
                        <option value="financial">Financial Services</option>
                        <option value="maintenance">Maintenance</option>
                        <option value="inspection">Inspection</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>Location</label>
                    <select id="locationFilter">
                        <option value="">All Locations</option>
                        <option value="mumbai">Mumbai</option>
                        <option value="delhi">Delhi</option>
                        <option value="bangalore">Bangalore</option>
                        <option value="pune">Pune</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>Price Range</label>
                    <select id="priceFilter">
                        <option value="">Any Price</option>
                        <option value="0-5000">₹0 - ₹5,000</option>
                        <option value="5000-15000">₹5,000 - ₹15,000</option>
                        <option value="15000-50000">₹15,000 - ₹50,000</option>
                        <option value="50000+">₹50,000+</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>Rating</label>
                    <select id="ratingFilter">
                        <option value="">Any Rating</option>
                        <option value="4.5">4.5+ Stars</option>
                        <option value="4.0">4.0+ Stars</option>
                        <option value="3.5">3.5+ Stars</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Services Grid -->
        <div class="services-grid" id="servicesGrid">
            <div class="loading-message">Loading services...</div>
        </div>

        <!-- Success/Error Messages -->
        <div class="message-container" id="messageContainer"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='/js/firebase-config.js') }}"></script>
    <script src="{{ url_for('static', path='/js/auth.js') }}"></script>
    <script src="{{ url_for('static', path='/js/services.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeServices();
        });

        function filterByCategory(category) {
            const categoryFilter = document.getElementById('categoryFilter');
            if (categoryFilter) {
                categoryFilter.value = category;
                searchServices();
            }
        }

        function searchServices() {
            if (window.servicesManager) {
                window.servicesManager.searchServices();
            }
        }
    </script>
</body>
</html>
