// Services JavaScript Module
class ServicesManager {
    constructor() {
        this.apiBaseUrl = '/api/v1';
        this.currentFilters = {};
        this.loadServices();
    }

    // Load services
    async loadServices() {
        try {
            const params = new URLSearchParams();
            
            // Add filters
            Object.keys(this.currentFilters).forEach(key => {
                if (this.currentFilters[key]) {
                    params.append(key, this.currentFilters[key]);
                }
            });

            const response = await fetch(`${this.apiBaseUrl}/services/?${params.toString()}`);
            
            if (response.ok) {
                const services = await response.json();
                this.displayServices(services);
            } else {
                throw new Error('Failed to load services');
            }
        } catch (error) {
            console.error('Error loading services:', error);
            this.displayError('Failed to load services. Please try again.');
        }
    }

    // Display services
    displayServices(services) {
        const grid = document.getElementById('servicesGrid');
        if (!grid) return;

        if (!services || services.length === 0) {
            grid.innerHTML = `
                <div class="empty-message">
                    <i class="fas fa-tools" style="font-size: 3rem; color: #ccc; margin-bottom: 20px;"></i>
                    <h3>No services found</h3>
                    <p>Try adjusting your search filters or search terms</p>
                </div>
            `;
            return;
        }

        const servicesHTML = services.map(service => this.createServiceCard(service)).join('');
        grid.innerHTML = servicesHTML;
    }

    // Create service card HTML
    createServiceCard(service) {
        const formattedPrice = service.price ? 
            new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR',
                maximumFractionDigits: 0
            }).format(service.price) : 'Contact for Price';

        const rating = service.rating || 4.5;
        const starsHTML = this.generateStars(rating);

        return `
            <div class="service-card" onclick="viewService(${service.id})">
                <div class="service-image">
                    <i class="fas fa-${this.getServiceIcon(service.category)}"></i>
                    <div class="service-badge">${service.category || 'Service'}</div>
                </div>
                <div class="service-content">
                    <div class="service-title">${service.title || 'Professional Service'}</div>
                    <div class="service-provider">
                        <i class="fas fa-user"></i>
                        ${service.provider_name || 'Service Provider'}
                    </div>
                    <div class="service-details">
                        <span>${starsHTML} (${service.reviews_count || 0} reviews)</span>
                        <span><i class="fas fa-map-marker-alt"></i> ${service.location || 'Multiple Locations'}</span>
                    </div>
                    <div class="service-price">${formattedPrice}</div>
                    <div class="service-actions" onclick="event.stopPropagation()">
                        <button class="action-button btn-primary" onclick="bookService(${service.id})">
                            Book Now
                        </button>
                        <button class="action-button btn-secondary" onclick="viewService(${service.id})">
                            View Details
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Get service icon based on category
    getServiceIcon(category) {
        const icons = {
            'legal': 'gavel',
            'financial': 'calculator',
            'maintenance': 'tools',
            'inspection': 'search',
            'default': 'concierge-bell'
        };
        return icons[category] || icons.default;
    }

    // Generate star rating HTML
    generateStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;
        let starsHTML = '';

        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star" style="color: #ffc107;"></i>';
        }

        if (hasHalfStar) {
            starsHTML += '<i class="fas fa-star-half-alt" style="color: #ffc107;"></i>';
        }

        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star" style="color: #ffc107;"></i>';
        }

        return starsHTML;
    }

    // Search services
    async searchServices() {
        const searchInput = document.getElementById('searchInput');
        const categoryFilter = document.getElementById('categoryFilter');
        const locationFilter = document.getElementById('locationFilter');
        const priceFilter = document.getElementById('priceFilter');
        const ratingFilter = document.getElementById('ratingFilter');

        // Build filters
        this.currentFilters = {};
        
        if (searchInput?.value) this.currentFilters.query = searchInput.value;
        if (categoryFilter?.value) this.currentFilters.category = categoryFilter.value;
        if (locationFilter?.value) this.currentFilters.location = locationFilter.value;
        if (priceFilter?.value) this.currentFilters.price_range = priceFilter.value;
        if (ratingFilter?.value) this.currentFilters.min_rating = parseFloat(ratingFilter.value);

        // Load services with filters
        await this.loadServices();
    }

    // Display error
    displayError(message) {
        const grid = document.getElementById('servicesGrid');
        if (grid) {
            grid.innerHTML = `
                <div class="empty-message">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ff4757; margin-bottom: 20px;"></i>
                    <h3>Error</h3>
                    <p>${message}</p>
                </div>
            `;
        }
    }

    // Book service
    async bookService(serviceId) {
        try {
            const token = await authManager.getCurrentUserToken();
            if (!token) {
                authManager.showMessage('Please log in to book services', 'error');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
                return;
            }

            // For now, show a message that booking is coming soon
            authManager.showMessage('Service booking feature coming soon!', 'info');
            
            // In a real implementation, you would:
            // 1. Open a booking modal or redirect to booking page
            // 2. Collect booking details (date, time, requirements)
            // 3. Submit booking request to API
            // 4. Handle payment if required
            
        } catch (error) {
            console.error('Error booking service:', error);
            authManager.showMessage('Failed to book service. Please try again.', 'error');
        }
    }
}

// Global functions
function initializeServices() {
    window.servicesManager = new ServicesManager();

    // Setup search on Enter key
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                searchServices();
            }
        });
    }

    // Setup filter change listeners
    const filters = ['categoryFilter', 'locationFilter', 'priceFilter', 'ratingFilter'];
    filters.forEach(filterId => {
        const element = document.getElementById(filterId);
        if (element) {
            element.addEventListener('change', () => {
                searchServices();
            });
        }
    });
}

function searchServices() {
    if (window.servicesManager) {
        window.servicesManager.searchServices();
    }
}

function viewService(serviceId) {
    window.location.href = `/services/${serviceId}`;
}

function bookService(serviceId) {
    if (window.servicesManager) {
        window.servicesManager.bookService(serviceId);
    }
}

// Export for use in other files
window.initializeServices = initializeServices;
