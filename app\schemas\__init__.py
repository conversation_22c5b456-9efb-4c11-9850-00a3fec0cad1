# This makes the schemas available as a package
from .users import UserBase, UserCreate, UserUpdate, UserInDB
from .properties import (
    PropertyBase, 
    PropertyCreate, 
    PropertyUpdate, 
    PropertyOut,
    PropertyImage,
    PropertyFeature
)
from .investments import (
    InvestmentBase,
    InvestmentCreate,
    InvestmentOut,
    InvestmentDocument
)
from .services import (
    ServiceProviderBase,
    ServiceProviderCreate,
    ServiceProviderOut,
    ServiceBookingBase,
    ServiceBookingCreate,
    ServiceBookingOut
)

__all__ = [
    'UserBase', 'UserCreate', 'UserUpdate', 'UserInDB',
    'PropertyBase', 'PropertyCreate', 'PropertyUpdate', 'PropertyOut',
    'PropertyImage', 'PropertyFeature',
    'InvestmentBase', 'InvestmentCreate', 'InvestmentOut', 'InvestmentDocument',
    'ServiceProviderBase', 'ServiceProviderCreate', 'ServiceProviderOut',
    'ServiceBookingBase', 'ServiceBookingCreate', 'ServiceBookingOut'
]